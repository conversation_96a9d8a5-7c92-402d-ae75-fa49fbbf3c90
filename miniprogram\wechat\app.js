// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'https://your-domain.com/backend/api', // 请修改为实际的API地址
    isLoggedIn: false
  },

  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }
  },

  // 登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: res => {
          if (res.code) {
            // 发送 res.code 到后台换取 openId, sessionKey, unionId
            this.request({
              url: '/user/login',
              method: 'POST',
              data: {
                code: res.code
              }
            }).then(result => {
              if (result.code === 200) {
                this.globalData.token = result.data.token
                this.globalData.userInfo = result.data.userInfo
                this.globalData.isLoggedIn = true
                
                wx.setStorageSync('token', result.data.token)
                wx.setStorageSync('userInfo', result.data.userInfo)
                
                resolve(result.data)
              } else {
                reject(result.message)
              }
            }).catch(reject)
          } else {
            reject('登录失败！' + res.errMsg)
          }
        },
        fail: reject
      })
    })
  },

  // 退出登录
  logout() {
    this.globalData.token = null
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
    
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.globalData.token ? `Bearer ${this.globalData.token}` : '',
          ...options.header
        },
        success: res => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // 未授权，清除登录状态
            this.logout()
            wx.showToast({
              title: '请重新登录',
              icon: 'none'
            })
            reject('未授权')
          } else {
            reject(res.data?.message || '请求失败')
          }
        },
        fail: err => {
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 隐藏加载中
  hideLoading() {
    wx.hideLoading()
  },

  // 显示消息提示
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    })
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours()
    const minute = date.getMinutes()
    const second = date.getSeconds()

    return `${[year, month, day].map(this.formatNumber).join('/')} ${[hour, minute, second].map(this.formatNumber).join(':')}`
  },

  formatNumber(n) {
    n = n.toString()
    return n[1] ? n : `0${n}`
  }
})
