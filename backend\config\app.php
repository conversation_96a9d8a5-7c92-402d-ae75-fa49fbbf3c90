<?php
/**
 * 应用配置文件
 */

return [
    'name' => '随叫随到上门服务管理系统',
    'version' => '1.0.0',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'timezone' => 'Asia/Shanghai',
    
    // 加密密钥
    'key' => $_ENV['APP_KEY'] ?? 'base64:' . base64_encode(random_bytes(32)),
    
    // 会话配置
    'session' => [
        'name' => 'SHANGMENFUWU_SESSION',
        'lifetime' => 7200, // 2小时
        'path' => '/',
        'domain' => null,
        'secure' => false,
        'httponly' => true,
    ],
    
    // 文件上传配置
    'upload' => [
        'max_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'path' => 'uploads/',
    ],
    
    // 微信小程序配置
    'wechat' => [
        'appid' => $_ENV['WECHAT_APPID'] ?? '',
        'secret' => $_ENV['WECHAT_SECRET'] ?? '',
    ],
    
    // 抖音小程序配置
    'douyin' => [
        'appid' => $_ENV['DOUYIN_APPID'] ?? '',
        'secret' => $_ENV['DOUYIN_SECRET'] ?? '',
    ],
    
    // 短信配置
    'sms' => [
        'provider' => $_ENV['SMS_PROVIDER'] ?? 'aliyun',
        'access_key' => $_ENV['SMS_ACCESS_KEY'] ?? '',
        'access_secret' => $_ENV['SMS_ACCESS_SECRET'] ?? '',
        'sign_name' => $_ENV['SMS_SIGN_NAME'] ?? '上门服务',
    ],
    
    // 支付配置
    'payment' => [
        'wechat' => [
            'mch_id' => $_ENV['WECHAT_MCH_ID'] ?? '',
            'key' => $_ENV['WECHAT_PAY_KEY'] ?? '',
        ],
    ],
];
