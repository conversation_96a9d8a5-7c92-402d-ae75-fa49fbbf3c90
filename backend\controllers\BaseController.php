<?php
/**
 * 基础控制器类
 */
abstract class BaseController
{
    protected $request;
    protected $response;

    public function __construct()
    {
        $this->request = $_REQUEST;
        $this->response = [];
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
    }

    /**
     * 成功响应
     */
    protected function success($data = null, $message = '操作成功', $code = 200)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 错误响应
     */
    protected function error($message = '操作失败', $code = 400, $data = null)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 分页响应
     */
    protected function paginate($data, $message = '获取成功')
    {
        $response = [
            'code' => 200,
            'message' => $message,
            'data' => $data['data'],
            'pagination' => [
                'total' => $data['total'],
                'per_page' => $data['per_page'],
                'current_page' => $data['current_page'],
                'last_page' => $data['last_page'],
                'from' => $data['from'] ?? null,
                'to' => $data['to'] ?? null
            ],
            'timestamp' => time()
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 获取请求参数
     */
    protected function getParam($key, $default = null)
    {
        return $this->request[$key] ?? $default;
    }

    /**
     * 获取POST数据
     */
    protected function getPostData()
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?: [];
    }

    /**
     * 验证必需参数
     */
    protected function validateRequired($params, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            if (!isset($params[$field]) || empty($params[$field])) {
                $errors[] = $rule['message'] ?? "{$field}不能为空";
            }
        }

        if (!empty($errors)) {
            $this->error(implode(', ', $errors), 422);
        }
    }

    /**
     * 验证参数格式
     */
    protected function validateFormat($params, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            if (!isset($params[$field])) {
                continue;
            }

            $value = $params[$field];
            
            // 验证长度
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[] = "{$field}长度不能少于{$rule['min_length']}位";
            }
            
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[] = "{$field}长度不能超过{$rule['max_length']}位";
            }

            // 验证正则表达式
            if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                $errors[] = $rule['message'] ?? "{$field}格式不正确";
            }

            // 验证数值范围
            if (isset($rule['min']) && $value < $rule['min']) {
                $errors[] = "{$field}不能小于{$rule['min']}";
            }
            
            if (isset($rule['max']) && $value > $rule['max']) {
                $errors[] = "{$field}不能大于{$rule['max']}";
            }
        }

        if (!empty($errors)) {
            $this->error(implode(', ', $errors), 422);
        }
    }

    /**
     * 检查管理员权限
     */
    protected function checkAdminAuth()
    {
        session_start();
        
        if (!isset($_SESSION['admin_id'])) {
            $this->error('请先登录', 401);
        }

        return $_SESSION['admin_id'];
    }

    /**
     * 记录操作日志
     */
    protected function logOperation($action, $data = [])
    {
        // 这里可以实现操作日志记录
        error_log(date('Y-m-d H:i:s') . " - {$action}: " . json_encode($data));
    }
}
