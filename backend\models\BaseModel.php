<?php
require_once __DIR__ . '/../utils/Database.php';

/**
 * 基础模型类
 */
abstract class BaseModel
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 查找所有记录
     */
    public function all($conditions = [], $orderBy = null, $limit = null)
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }

        $results = $this->db->fetchAll($sql, $params);
        return $this->hideFields($results);
    }

    /**
     * 根据ID查找记录
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $result = $this->db->fetchOne($sql, ['id' => $id]);
        return $result ? $this->hideFields([$result])[0] : null;
    }

    /**
     * 根据条件查找单条记录
     */
    public function findWhere($conditions)
    {
        $whereClause = [];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = "{$field} = :{$field}";
            $params[$field] = $value;
        }

        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $whereClause);
        $result = $this->db->fetchOne($sql, $params);
        return $result ? $this->hideFields([$result])[0] : null;
    }

    /**
     * 创建记录
     */
    public function create($data)
    {
        $filteredData = $this->filterFillable($data);
        $filteredData['created_at'] = date('Y-m-d H:i:s');
        $filteredData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $filteredData);
    }

    /**
     * 更新记录
     */
    public function update($id, $data)
    {
        $filteredData = $this->filterFillable($data);
        $filteredData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update(
            $this->table, 
            $filteredData, 
            "{$this->primaryKey} = :id", 
            ['id' => $id]
        );
    }

    /**
     * 删除记录
     */
    public function delete($id)
    {
        return $this->db->delete($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }

    /**
     * 分页查询
     */
    public function paginate($page = 1, $perPage = 15, $conditions = [], $orderBy = null)
    {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = '';
        $params = [];
        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $field => $value) {
                $whereParts[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $whereClause = " WHERE " . implode(' AND ', $whereParts);
        }

        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->table}" . $whereClause;
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];

        // 获取数据
        $sql = "SELECT * FROM {$this->table}" . $whereClause;
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";

        $data = $this->db->fetchAll($sql, $params);
        $data = $this->hideFields($data);

        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }

    /**
     * 过滤可填充字段
     */
    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * 隐藏指定字段
     */
    protected function hideFields($data)
    {
        if (empty($this->hidden) || empty($data)) {
            return $data;
        }

        foreach ($data as &$item) {
            foreach ($this->hidden as $field) {
                unset($item[$field]);
            }
        }

        return $data;
    }
}
