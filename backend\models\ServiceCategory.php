<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * 服务分类模型
 */
class ServiceCategory extends BaseModel
{
    protected $table = 'service_categories';
    protected $fillable = [
        'parent_id', 'name', 'description', 'icon', 'sort_order', 'status'
    ];

    /**
     * 获取顶级分类
     */
    public function getTopCategories()
    {
        return $this->all(['parent_id' => 0, 'status' => 1], 'sort_order ASC');
    }

    /**
     * 获取子分类
     */
    public function getSubCategories($parentId)
    {
        return $this->all(['parent_id' => $parentId, 'status' => 1], 'sort_order ASC');
    }

    /**
     * 获取分类树形结构
     */
    public function getCategoryTree()
    {
        $topCategories = $this->getTopCategories();
        
        foreach ($topCategories as &$category) {
            $category['children'] = $this->getSubCategories($category['id']);
        }
        
        return $topCategories;
    }

    /**
     * 获取所有启用的分类（扁平结构）
     */
    public function getAllActiveCategories()
    {
        return $this->all(['status' => 1], 'parent_id ASC, sort_order ASC');
    }

    /**
     * 更新分类状态
     */
    public function updateStatus($id, $status)
    {
        return $this->update($id, ['status' => $status]);
    }

    /**
     * 更新排序
     */
    public function updateSortOrder($id, $sortOrder)
    {
        return $this->update($id, ['sort_order' => $sortOrder]);
    }

    /**
     * 检查分类是否有子分类
     */
    public function hasChildren($id)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE parent_id = :id";
        $result = $this->db->fetchOne($sql, ['id' => $id]);
        return $result['count'] > 0;
    }

    /**
     * 检查分类是否有关联的服务
     */
    public function hasServices($id)
    {
        $sql = "SELECT COUNT(*) as count FROM services WHERE category_id = :id";
        $result = $this->db->fetchOne($sql, ['id' => $id]);
        return $result['count'] > 0;
    }

    /**
     * 删除分类（检查是否可以删除）
     */
    public function deleteCategory($id)
    {
        // 检查是否有子分类
        if ($this->hasChildren($id)) {
            throw new Exception('该分类下还有子分类，无法删除');
        }

        // 检查是否有关联的服务
        if ($this->hasServices($id)) {
            throw new Exception('该分类下还有服务项目，无法删除');
        }

        return $this->delete($id);
    }
}
