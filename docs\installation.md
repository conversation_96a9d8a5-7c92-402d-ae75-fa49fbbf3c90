# 安装部署指南

## 环境要求

### 服务器环境
- PHP 7.4 或更高版本
- MySQL 5.7 或更高版本
- Apache 或 Nginx Web服务器
- PHP扩展：PDO、PDO_MySQL、JSON、OpenSSL、cURL

### 开发环境
- 微信开发者工具
- 抖音开发者工具（可选）
- 代码编辑器（推荐 VS Code）

## 安装步骤

### 1. 下载项目代码
```bash
git clone <repository-url>
cd shangmenfuwu
```

### 2. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入实际的配置信息
nano .env
```

### 3. 配置数据库

#### 3.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE shangmenfuwu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建数据库用户（可选）
CREATE USER 'shangmenfuwu'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON shangmenfuwu_db.* TO 'shangmenfuwu'@'localhost';
FLUSH PRIVILEGES;
```

#### 3.2 导入数据库结构
```bash
# 导入数据库结构
mysql -u root -p shangmenfuwu_db < database/migrations/001_create_database.sql
mysql -u root -p shangmenfuwu_db < database/migrations/002_create_orders.sql

# 导入初始数据
mysql -u root -p shangmenfuwu_db < database/seeds/001_init_data.sql
```

### 4. 配置Web服务器

#### Apache配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/shangmenfuwu
    
    <Directory /path/to/shangmenfuwu>
        AllowOverride All
        Require all granted
    </Directory>
    
    # 设置后台目录
    Alias /admin /path/to/shangmenfuwu/backend/admin
    Alias /api /path/to/shangmenfuwu/backend/api
    
    ErrorLog ${APACHE_LOG_DIR}/shangmenfuwu_error.log
    CustomLog ${APACHE_LOG_DIR}/shangmenfuwu_access.log combined
</VirtualHost>
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/shangmenfuwu;
    index index.php index.html;

    # API接口
    location /api/ {
        alias /path/to/shangmenfuwu/backend/api/;
        try_files $uri $uri/ @php;
    }

    # 后台管理
    location /admin/ {
        alias /path/to/shangmenfuwu/backend/admin/;
        try_files $uri $uri/ @php;
    }

    # PHP处理
    location @php {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 5. 设置文件权限
```bash
# 设置上传目录权限
mkdir -p uploads
chmod 755 uploads
chown www-data:www-data uploads

# 设置日志目录权限
mkdir -p logs
chmod 755 logs
chown www-data:www-data logs
```

### 6. 配置小程序

#### 6.1 微信小程序配置
1. 在微信公众平台注册小程序账号
2. 获取 AppID 和 AppSecret
3. 修改 `miniprogram/wechat/app.js` 中的 `baseUrl`
4. 在 `.env` 文件中配置微信相关参数

#### 6.2 抖音小程序配置
1. 在抖音开放平台注册小程序账号
2. 获取 AppID 和 AppSecret
3. 配置相关参数

## 测试安装

### 1. 测试后台管理系统
访问：`http://your-domain.com/admin`
默认管理员账号：
- 用户名：admin
- 密码：password

### 2. 测试API接口
```bash
# 测试登录接口
curl -X POST http://your-domain.com/api/admin/login.php \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

### 3. 测试小程序
1. 使用微信开发者工具打开 `miniprogram/wechat` 目录
2. 配置 AppID
3. 预览和调试

## 常见问题

### 1. 数据库连接失败
- 检查 `.env` 文件中的数据库配置
- 确认MySQL服务是否启动
- 检查数据库用户权限

### 2. PHP错误
- 检查PHP版本是否符合要求
- 确认必需的PHP扩展已安装
- 检查文件权限设置

### 3. 小程序无法连接API
- 检查小程序中的 `baseUrl` 配置
- 确认服务器域名已在小程序后台配置
- 检查HTTPS证书配置

### 4. 文件上传失败
- 检查上传目录权限
- 确认PHP上传配置
- 检查磁盘空间

## 生产环境部署

### 1. 安全配置
- 修改默认管理员密码
- 配置HTTPS证书
- 设置防火墙规则
- 配置数据库安全

### 2. 性能优化
- 启用PHP OPcache
- 配置数据库索引
- 设置缓存策略
- 优化图片资源

### 3. 监控和日志
- 配置错误日志
- 设置性能监控
- 配置备份策略

## 更新升级

### 1. 备份数据
```bash
# 备份数据库
mysqldump -u root -p shangmenfuwu_db > backup_$(date +%Y%m%d).sql

# 备份文件
tar -czf backup_files_$(date +%Y%m%d).tar.gz uploads/
```

### 2. 更新代码
```bash
git pull origin main
```

### 3. 更新数据库
```bash
# 如有新的迁移文件，执行更新
mysql -u root -p shangmenfuwu_db < database/migrations/new_migration.sql
```
