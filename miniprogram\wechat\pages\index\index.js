// index.js
const app = getApp()

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/banner1.jpg'
      },
      {
        id: 2,
        image: '/images/banner2.jpg'
      },
      {
        id: 3,
        image: '/images/banner3.jpg'
      }
    ],
    categories: [],
    hotServices: [],
    technicians: []
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  // 加载页面数据
  loadData() {
    this.loadCategories()
    this.loadHotServices()
    this.loadTechnicians()
  },

  // 加载服务分类
  loadCategories() {
    app.request({
      url: '/category/list',
      method: 'GET'
    }).then(res => {
      if (res.code === 200) {
        this.setData({
          categories: res.data.slice(0, 8) // 只显示前8个分类
        })
      }
    }).catch(err => {
      console.error('加载分类失败:', err)
    })
  },

  // 加载热门服务
  loadHotServices() {
    app.request({
      url: '/service/hot',
      method: 'GET'
    }).then(res => {
      if (res.code === 200) {
        this.setData({
          hotServices: res.data.slice(0, 6) // 只显示前6个服务
        })
      }
    }).catch(err => {
      console.error('加载热门服务失败:', err)
      // 使用模拟数据
      this.setData({
        hotServices: [
          {
            id: 1,
            name: '电脑系统重装',
            description: '专业电脑系统重装，包含常用软件安装',
            price: 80,
            unit: '次',
            image: '/images/service1.jpg'
          },
          {
            id: 2,
            name: '空调清洗',
            description: '专业空调深度清洗，杀菌除味',
            price: 120,
            unit: '台',
            image: '/images/service2.jpg'
          },
          {
            id: 3,
            name: 'iPhone换屏',
            description: 'iPhone手机屏幕更换，原装配件',
            price: 280,
            unit: '次',
            image: '/images/service3.jpg'
          }
        ]
      })
    })
  },

  // 加载优秀技师
  loadTechnicians() {
    app.request({
      url: '/technician/recommend',
      method: 'GET'
    }).then(res => {
      if (res.code === 200) {
        this.setData({
          technicians: res.data.slice(0, 10)
        })
      }
    }).catch(err => {
      console.error('加载技师失败:', err)
      // 使用模拟数据
      this.setData({
        technicians: [
          {
            id: 1,
            name: '张师傅',
            avatar: '/images/avatar1.jpg',
            rating: 4.9,
            skills: '电脑维修专家'
          },
          {
            id: 2,
            name: '李师傅',
            avatar: '/images/avatar2.jpg',
            rating: 4.8,
            skills: '家电维修专家'
          },
          {
            id: 3,
            name: '王师傅',
            avatar: '/images/avatar3.jpg',
            rating: 4.7,
            skills: '手机维修专家'
          }
        ]
      })
    })
  },

  // 搜索点击
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 分类点击
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/category/category?id=${categoryId}`
    })
  },

  // 服务点击
  onServiceTap(e) {
    const serviceId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/service/service?id=${serviceId}`
    })
  },

  // 更多服务
  onMoreServiceTap() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 技师点击
  onTechnicianTap(e) {
    const technicianId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/technician/technician?id=${technicianId}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
