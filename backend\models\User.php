<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * 用户模型
 */
class User extends BaseModel
{
    protected $table = 'users';
    protected $fillable = [
        'openid', 'unionid', 'nickname', 'avatar', 'phone', 
        'gender', 'user_type', 'status'
    ];

    /**
     * 根据openid查找用户
     */
    public function findByOpenid($openid)
    {
        return $this->findWhere(['openid' => $openid]);
    }

    /**
     * 根据手机号查找用户
     */
    public function findByPhone($phone)
    {
        return $this->findWhere(['phone' => $phone]);
    }

    /**
     * 获取技师列表
     */
    public function getTechnicians($conditions = [])
    {
        $conditions['user_type'] = 2; // 技师类型
        return $this->all($conditions, 'created_at DESC');
    }

    /**
     * 获取普通用户列表
     */
    public function getCustomers($conditions = [])
    {
        $conditions['user_type'] = 1; // 普通用户类型
        return $this->all($conditions, 'created_at DESC');
    }

    /**
     * 更新用户状态
     */
    public function updateStatus($id, $status)
    {
        return $this->update($id, ['status' => $status]);
    }

    /**
     * 统计用户数量
     */
    public function getStatistics()
    {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN user_type = 1 THEN 1 ELSE 0 END) as customers,
                    SUM(CASE WHEN user_type = 2 THEN 1 ELSE 0 END) as technicians,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_new
                FROM {$this->table}";
        
        return $this->db->fetchOne($sql);
    }

    /**
     * 获取用户注册趋势
     */
    public function getRegistrationTrend($days = 7)
    {
        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as count
                FROM {$this->table} 
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        return $this->db->fetchAll($sql);
    }
}
