<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随叫随到上门服务 - 后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-form {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 400px;
        }
        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 24px;
            font-weight: 500;
        }
        .admin-layout {
            height: 100vh;
        }
        .admin-header {
            background: #545c64;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        .admin-main {
            height: calc(100vh - 60px);
        }
        .admin-sidebar {
            background: #f5f5f5;
            height: 100%;
        }
        .admin-content {
            padding: 20px;
            height: 100%;
            overflow-y: auto;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #409eff;
        }
        .stats-label {
            color: #666;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div v-if="!isLoggedIn" class="login-container">
            <div class="login-form">
                <h2 class="login-title">后台管理系统</h2>
                <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
                    <el-form-item prop="username">
                        <el-input
                            v-model="loginForm.username"
                            placeholder="请输入用户名"
                            prefix-icon="el-icon-user">
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input
                            v-model="loginForm.password"
                            type="password"
                            placeholder="请输入密码"
                            prefix-icon="el-icon-lock"
                            @keyup.enter.native="handleLogin">
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            style="width: 100%"
                            :loading="loginLoading"
                            @click="handleLogin">
                            登录
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 管理后台 -->
        <div v-else class="admin-layout">
            <el-container>
                <el-header class="admin-header">
                    <div>
                        <h3>随叫随到上门服务管理系统</h3>
                    </div>
                    <div>
                        <span>欢迎，{{ adminInfo.real_name || adminInfo.username }}</span>
                        <el-button type="text" style="color: white; margin-left: 20px;" @click="handleLogout">
                            退出
                        </el-button>
                    </div>
                </el-header>
                
                <el-container class="admin-main">
                    <el-aside width="200px" class="admin-sidebar">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            background-color="#f5f5f5"
                            text-color="#333"
                            active-text-color="#409eff">
                            
                            <el-menu-item index="dashboard">
                                <i class="el-icon-s-home"></i>
                                <span>仪表板</span>
                            </el-menu-item>
                            
                            <el-submenu index="users">
                                <template slot="title">
                                    <i class="el-icon-user"></i>
                                    <span>用户管理</span>
                                </template>
                                <el-menu-item index="users/customers">普通用户</el-menu-item>
                                <el-menu-item index="users/technicians">技师管理</el-menu-item>
                            </el-submenu>
                            
                            <el-submenu index="services">
                                <template slot="title">
                                    <i class="el-icon-s-order"></i>
                                    <span>服务管理</span>
                                </template>
                                <el-menu-item index="services/categories">服务分类</el-menu-item>
                                <el-menu-item index="services/items">服务项目</el-menu-item>
                            </el-submenu>
                            
                            <el-menu-item index="orders">
                                <i class="el-icon-document"></i>
                                <span>订单管理</span>
                            </el-menu-item>
                            
                            <el-menu-item index="settings">
                                <i class="el-icon-setting"></i>
                                <span>系统设置</span>
                            </el-menu-item>
                        </el-menu>
                    </el-aside>
                    
                    <el-main class="admin-content">
                        <!-- 仪表板 -->
                        <div v-if="activeMenu === 'dashboard'">
                            <h2>仪表板</h2>
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <div class="stats-card">
                                        <div class="stats-number">{{ dashboard.user_stats?.total_users || 0 }}</div>
                                        <div class="stats-label">总用户数</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="stats-card">
                                        <div class="stats-number">{{ dashboard.user_stats?.technicians || 0 }}</div>
                                        <div class="stats-label">技师数量</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="stats-card">
                                        <div class="stats-number">{{ dashboard.order_stats?.total_orders || 0 }}</div>
                                        <div class="stats-label">总订单数</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="stats-card">
                                        <div class="stats-number">¥{{ dashboard.order_stats?.total_revenue || 0 }}</div>
                                        <div class="stats-label">总收入</div>
                                    </div>
                                </el-col>
                            </el-row>
                            
                            <div style="margin-top: 20px;">
                                <h3>最近订单</h3>
                                <el-table :data="dashboard.recent_orders" style="width: 100%">
                                    <el-table-column prop="order_no" label="订单号" width="180"></el-table-column>
                                    <el-table-column prop="user_nickname" label="用户" width="120"></el-table-column>
                                    <el-table-column prop="service_name" label="服务项目"></el-table-column>
                                    <el-table-column prop="service_fee" label="金额" width="100">
                                        <template slot-scope="scope">
                                            ¥{{ scope.row.service_fee }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" label="状态" width="100">
                                        <template slot-scope="scope">
                                            <el-tag :type="getStatusType(scope.row.status)">
                                                {{ getStatusText(scope.row.status) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
                                </el-table>
                            </div>
                        </div>
                        
                        <!-- 其他页面内容 -->
                        <div v-else>
                            <h2>{{ getPageTitle() }}</h2>
                            <p>{{ activeMenu }} 页面开发中...</p>
                        </div>
                    </el-main>
                </el-container>
            </el-container>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    isLoggedIn: false,
                    loginLoading: false,
                    activeMenu: 'dashboard',
                    adminInfo: {},
                    dashboard: {},
                    loginForm: {
                        username: '',
                        password: ''
                    },
                    loginRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' }
                        ]
                    }
                }
            },
            mounted() {
                this.checkLoginStatus();
            },
            methods: {
                checkLoginStatus() {
                    axios.get('../api/admin/info.php')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.isLoggedIn = true;
                                this.adminInfo = response.data.data;
                                this.loadDashboard();
                            }
                        })
                        .catch(() => {
                            this.isLoggedIn = false;
                        });
                },
                
                handleLogin() {
                    this.$refs.loginForm.validate((valid) => {
                        if (valid) {
                            this.loginLoading = true;
                            axios.post('../api/admin/login.php', this.loginForm)
                                .then(response => {
                                    if (response.data.code === 200) {
                                        this.isLoggedIn = true;
                                        this.adminInfo = response.data.data;
                                        this.loadDashboard();
                                        this.$message.success('登录成功');
                                    } else {
                                        this.$message.error(response.data.message);
                                    }
                                })
                                .catch(error => {
                                    this.$message.error('登录失败');
                                })
                                .finally(() => {
                                    this.loginLoading = false;
                                });
                        }
                    });
                },
                
                handleLogout() {
                    axios.post('../api/admin/logout.php')
                        .then(() => {
                            this.isLoggedIn = false;
                            this.adminInfo = {};
                            this.dashboard = {};
                            this.$message.success('退出成功');
                        });
                },
                
                loadDashboard() {
                    axios.get('../api/admin/dashboard.php')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.dashboard = response.data.data;
                            }
                        });
                },
                
                handleMenuSelect(index) {
                    this.activeMenu = index;
                },
                
                getPageTitle() {
                    const titles = {
                        'dashboard': '仪表板',
                        'users/customers': '普通用户管理',
                        'users/technicians': '技师管理',
                        'services/categories': '服务分类管理',
                        'services/items': '服务项目管理',
                        'orders': '订单管理',
                        'settings': '系统设置'
                    };
                    return titles[this.activeMenu] || '页面';
                },
                
                getStatusType(status) {
                    const types = {
                        1: 'warning',  // 待接单
                        2: 'primary',  // 已接单
                        3: 'success',  // 服务中
                        4: 'success',  // 已完成
                        5: 'danger',   // 已取消
                        6: 'info'      // 已评价
                    };
                    return types[status] || 'info';
                },
                
                getStatusText(status) {
                    const texts = {
                        1: '待接单',
                        2: '已接单',
                        3: '服务中',
                        4: '已完成',
                        5: '已取消',
                        6: '已评价'
                    };
                    return texts[status] || '未知';
                }
            }
        });
    </script>
</body>
</html>
