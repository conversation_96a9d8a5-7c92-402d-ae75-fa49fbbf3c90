<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * 订单模型
 */
class Order extends BaseModel
{
    protected $table = 'orders';
    protected $fillable = [
        'order_no', 'user_id', 'technician_id', 'service_id',
        'contact_name', 'contact_phone', 'address', 'appointment_time',
        'problem_desc', 'service_fee', 'status'
    ];

    // 订单状态常量
    const STATUS_PENDING = 1;      // 待接单
    const STATUS_ACCEPTED = 2;     // 已接单
    const STATUS_IN_SERVICE = 3;   // 服务中
    const STATUS_COMPLETED = 4;    // 已完成
    const STATUS_CANCELLED = 5;    // 已取消
    const STATUS_REVIEWED = 6;     // 已评价

    /**
     * 生成订单号
     */
    public function generateOrderNo()
    {
        return date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 创建订单
     */
    public function createOrder($data)
    {
        $data['order_no'] = $this->generateOrderNo();
        $data['status'] = self::STATUS_PENDING;
        
        $this->db->beginTransaction();
        try {
            $orderId = $this->create($data);
            
            // 记录订单状态日志
            $this->addStatusLog($orderId, self::STATUS_PENDING, '订单创建');
            
            $this->db->commit();
            return $orderId;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 更新订单状态
     */
    public function updateStatus($id, $status, $remark = '')
    {
        $this->db->beginTransaction();
        try {
            $this->update($id, ['status' => $status]);
            $this->addStatusLog($id, $status, $remark);
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 添加状态日志
     */
    private function addStatusLog($orderId, $status, $remark = '')
    {
        $sql = "INSERT INTO order_status_logs (order_id, status, remark, created_at) VALUES (?, ?, ?, ?)";
        $this->db->query($sql, [$orderId, $status, $remark, date('Y-m-d H:i:s')]);
    }

    /**
     * 获取订单详情（包含关联信息）
     */
    public function getOrderDetail($id)
    {
        $sql = "SELECT 
                    o.*,
                    u.nickname as user_nickname,
                    u.avatar as user_avatar,
                    t.real_name as technician_name,
                    tu.phone as technician_phone,
                    s.name as service_name,
                    sc.name as category_name
                FROM {$this->table} o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN technicians t ON o.technician_id = t.id
                LEFT JOIN users tu ON t.user_id = tu.id
                LEFT JOIN services s ON o.service_id = s.id
                LEFT JOIN service_categories sc ON s.category_id = sc.id
                WHERE o.id = :id";
        
        return $this->db->fetchOne($sql, ['id' => $id]);
    }

    /**
     * 获取订单列表（包含关联信息）
     */
    public function getOrderList($conditions = [], $page = 1, $perPage = 15)
    {
        $whereClause = '';
        $params = [];
        
        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $field => $value) {
                if ($field === 'status' && is_array($value)) {
                    $placeholders = implode(',', array_fill(0, count($value), '?'));
                    $whereParts[] = "o.status IN ({$placeholders})";
                    $params = array_merge($params, $value);
                } else {
                    $whereParts[] = "o.{$field} = ?";
                    $params[] = $value;
                }
            }
            $whereClause = " WHERE " . implode(' AND ', $whereParts);
        }

        $offset = ($page - 1) * $perPage;

        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->table} o" . $whereClause;
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];

        // 获取数据
        $sql = "SELECT 
                    o.*,
                    u.nickname as user_nickname,
                    t.real_name as technician_name,
                    s.name as service_name,
                    sc.name as category_name
                FROM {$this->table} o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN technicians t ON o.technician_id = t.id
                LEFT JOIN services s ON o.service_id = s.id
                LEFT JOIN service_categories sc ON s.category_id = sc.id
                {$whereClause}
                ORDER BY o.created_at DESC
                LIMIT {$perPage} OFFSET {$offset}";

        $data = $this->db->fetchAll($sql, $params);

        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }

    /**
     * 获取订单统计
     */
    public function getStatistics()
    {
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_orders,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as accepted_orders,
                    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as in_service_orders,
                    SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as completed_orders,
                    SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as cancelled_orders,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders,
                    SUM(CASE WHEN status = 4 THEN service_fee ELSE 0 END) as total_revenue
                FROM {$this->table}";
        
        return $this->db->fetchOne($sql);
    }

    /**
     * 获取状态名称
     */
    public static function getStatusName($status)
    {
        $statusNames = [
            self::STATUS_PENDING => '待接单',
            self::STATUS_ACCEPTED => '已接单',
            self::STATUS_IN_SERVICE => '服务中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REVIEWED => '已评价'
        ];

        return $statusNames[$status] ?? '未知状态';
    }
}
