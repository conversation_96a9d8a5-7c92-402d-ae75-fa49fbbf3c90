<?php
require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Order.php';

/**
 * 管理员控制器
 */
class AdminController extends BaseController
{
    private $userModel;
    private $orderModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->orderModel = new Order();
    }

    /**
     * 管理员登录
     */
    public function login()
    {
        $data = $this->getPostData();
        
        $this->validateRequired($data, [
            'username' => ['message' => '用户名不能为空'],
            'password' => ['message' => '密码不能为空']
        ]);

        $username = $data['username'];
        $password = $data['password'];

        try {
            $db = Database::getInstance();
            $admin = $db->fetchOne(
                "SELECT * FROM admins WHERE username = :username AND status = 1",
                ['username' => $username]
            );

            if (!$admin || !password_verify($password, $admin['password'])) {
                $this->error('用户名或密码错误');
            }

            // 更新最后登录时间
            $db->update('admins', 
                ['last_login_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $admin['id']]
            );

            // 设置会话
            session_start();
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_role'] = $admin['role'];

            unset($admin['password']);
            $this->success($admin, '登录成功');

        } catch (Exception $e) {
            $this->error('登录失败：' . $e->getMessage());
        }
    }

    /**
     * 管理员退出
     */
    public function logout()
    {
        session_start();
        session_destroy();
        $this->success(null, '退出成功');
    }

    /**
     * 获取管理员信息
     */
    public function getAdminInfo()
    {
        $adminId = $this->checkAdminAuth();
        
        try {
            $db = Database::getInstance();
            $admin = $db->fetchOne(
                "SELECT id, username, real_name, email, phone, role, last_login_at FROM admins WHERE id = :id",
                ['id' => $adminId]
            );

            $this->success($admin);

        } catch (Exception $e) {
            $this->error('获取管理员信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取仪表板数据
     */
    public function getDashboard()
    {
        $this->checkAdminAuth();

        try {
            // 获取用户统计
            $userStats = $this->userModel->getStatistics();
            
            // 获取订单统计
            $orderStats = $this->orderModel->getStatistics();
            
            // 获取最近订单
            $recentOrders = $this->orderModel->getOrderList([], 1, 10);
            
            // 获取用户注册趋势
            $userTrend = $this->userModel->getRegistrationTrend(7);

            $dashboard = [
                'user_stats' => $userStats,
                'order_stats' => $orderStats,
                'recent_orders' => $recentOrders['data'],
                'user_trend' => $userTrend
            ];

            $this->success($dashboard);

        } catch (Exception $e) {
            $this->error('获取仪表板数据失败：' . $e->getMessage());
        }
    }

    /**
     * 修改密码
     */
    public function changePassword()
    {
        $adminId = $this->checkAdminAuth();
        $data = $this->getPostData();

        $this->validateRequired($data, [
            'old_password' => ['message' => '原密码不能为空'],
            'new_password' => ['message' => '新密码不能为空'],
            'confirm_password' => ['message' => '确认密码不能为空']
        ]);

        if ($data['new_password'] !== $data['confirm_password']) {
            $this->error('两次输入的密码不一致');
        }

        if (strlen($data['new_password']) < 6) {
            $this->error('新密码长度不能少于6位');
        }

        try {
            $db = Database::getInstance();
            $admin = $db->fetchOne(
                "SELECT password FROM admins WHERE id = :id",
                ['id' => $adminId]
            );

            if (!password_verify($data['old_password'], $admin['password'])) {
                $this->error('原密码错误');
            }

            $newPasswordHash = password_hash($data['new_password'], PASSWORD_DEFAULT);
            $db->update('admins', 
                ['password' => $newPasswordHash], 
                'id = :id', 
                ['id' => $adminId]
            );

            $this->success(null, '密码修改成功');

        } catch (Exception $e) {
            $this->error('密码修改失败：' . $e->getMessage());
        }
    }
}
