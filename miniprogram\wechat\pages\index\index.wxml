<!--index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <icon class="search-icon" type="search" size="16"></icon>
      <text class="search-placeholder">搜索服务</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 服务分类 -->
  <view class="category-section">
    <view class="section-title">服务分类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}">
        <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 热门服务 -->
  <view class="service-section">
    <view class="section-title">
      <text>热门服务</text>
      <text class="more-link" bindtap="onMoreServiceTap">更多 ></text>
    </view>
    <view class="service-list">
      <view class="service-item" wx:for="{{hotServices}}" wx:key="id" bindtap="onServiceTap" data-id="{{item.id}}">
        <image class="service-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="service-info">
          <text class="service-name">{{item.name}}</text>
          <text class="service-desc">{{item.description}}</text>
          <view class="service-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
            <text class="price-unit">/{{item.unit}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 技师推荐 -->
  <view class="technician-section">
    <view class="section-title">优秀技师</view>
    <scroll-view class="technician-scroll" scroll-x="true">
      <view class="technician-list">
        <view class="technician-item" wx:for="{{technicians}}" wx:key="id" bindtap="onTechnicianTap" data-id="{{item.id}}">
          <image class="technician-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <text class="technician-name">{{item.name}}</text>
          <view class="technician-rating">
            <text class="rating-score">{{item.rating}}</text>
            <text class="rating-text">分</text>
          </view>
          <text class="technician-skills">{{item.skills}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
