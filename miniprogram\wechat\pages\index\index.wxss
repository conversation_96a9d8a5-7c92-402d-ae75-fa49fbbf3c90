/* index.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
}

/* 搜索栏 */
.search-bar {
  background-color: #fff;
  padding: 20rpx;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  margin-right: 20rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
  margin-bottom: 20rpx;
}

.banner-swiper {
  height: 300rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 服务分类 */
.category-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  font-size: 26rpx;
  color: #666;
  font-weight: normal;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
}

/* 热门服务 */
.service-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  padding: 20rpx;
  align-items: center;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6b35;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 5rpx;
}

/* 技师推荐 */
.technician-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.technician-scroll {
  white-space: nowrap;
}

.technician-list {
  display: inline-flex;
  gap: 20rpx;
}

.technician-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  width: 200rpx;
  text-align: center;
}

.technician-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.technician-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.technician-rating {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: bold;
}

.rating-text {
  font-size: 20rpx;
  color: #999;
  margin-left: 5rpx;
}

.technician-skills {
  font-size: 22rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
